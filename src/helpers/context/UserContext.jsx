import { createContext, useContext, useState, useEffect } from "react";
import { URL } from "../constant/Url";
import apiInstance from "../Axios/axiosINstance";
import siteConstant from "../constant/siteConstant";
import { fetchFromStorage } from "./storage";

const UserContext = createContext();

export const UserProvider = ({ children }) => {
  const [selectedUser, setSelectedUser] = useState(null);
  const [users, setUsers] = useState([]);
  const [loadingUser, setLoadingUser] = useState(true);
  const [usersLoaded, setUsersLoaded] = useState(false);
  const [userToken, setUserToken] = useState(null);

  // Effect to track user token changes
  useEffect(() => {
    const checkToken = () => {
      const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
      const token = userData?.token;

      // If token changes or is removed, reset user selection
      if (token !== userToken) {
        console.log("Token changed, resetting user selection");
        setUserToken(token);
        if (!token) {
          // User logged out - reset everything
          setSelectedUser(null);
          setUsers([]);
          setUsersLoaded(false);
          localStorage.removeItem("UserId");
        } else {
          // User logged in - trigger user fetch
          setUsersLoaded(false);
          setLoadingUser(true);
        }
      }
    };

    // Check token on mount and set up interval
    checkToken();
    const interval = setInterval(checkToken, 1000);

    return () => clearInterval(interval);
  }, [userToken]);

  // Fetch users from API
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
        // Only fetch users if the user is authenticated
        if (!userData?.token) return;

        setLoadingUser(true);
        const response = await apiInstance.get(URL.USER_MANAGEMENT_API);
        const fetchedUsers = response.data?.results?.data || [];
        console.log("Fetched users from API:", fetchedUsers);
        setUsers(fetchedUsers);
        setUsersLoaded(true);
      } catch (error) {
        console.error("Error fetching users:", error);
      } finally {
        setLoadingUser(false);
      }
    };

    if (userToken) {
      fetchUsers();
    }
  }, [userToken]); // Re-fetch users when user token changes

  // Auto-select user based on localStorage or default to first user
  useEffect(() => {
    if (!usersLoaded || users.length === 0 || !userToken) return;

    const storedUserId = localStorage.getItem("UserId");
    console.log("Stored user ID from localStorage:", storedUserId);

    if (!storedUserId) {
      // No stored user, select the first one
      const firstUser = users[0];
      console.log("No stored user, selecting first user:", firstUser);
      setSelectedUser(firstUser);
      localStorage.setItem("UserId", firstUser.id.toString());
      return;
    }

    const userId = parseInt(storedUserId, 10);
    console.log("Looking for user with ID:", userId);

    const userToSelect = users.find((u) => u.id === userId);
    console.log("Found user:", userToSelect);

    if (userToSelect) {
      console.log("Setting selected user to:", userToSelect);
      setSelectedUser(userToSelect);
    } else {
      console.log("Stored user not found in available users");
      // Only fall back to first user if the stored user is not found
      const firstUser = users[0];
      console.log("Falling back to first user:", firstUser);
      setSelectedUser(firstUser);
      localStorage.setItem("UserId", firstUser.id.toString());
    }
  }, [usersLoaded, users, userToken]); // Re-run when user token changes

  const handleUserSelect = (user) => {
    console.log("Handling user selection:", user);
    setSelectedUser(user);
    localStorage.setItem("UserId", user?.id?.toString());
  };

  const resetUser = () => {
    setSelectedUser(null);
    localStorage.removeItem("UserId");
  };

  // Method to refresh users after changes
  const refreshUsers = async () => {
    try {
      const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
      if (!userData?.token) return;

      setLoadingUser(true);
      const response = await apiInstance.get(URL.USER_MANAGEMENT_API);
      const fetchedUsers = response.data?.results?.data || [];
      setUsers(fetchedUsers);
    } catch (error) {
      console.error("Error refreshing users:", error);
    } finally {
      setLoadingUser(false);
    }
  };

  return (
    <UserContext.Provider
      value={{
        selectedUser,
        handleUserSelect,
        users,
        setUsers,
        resetUser,
        loadingUser,
        refreshUsers,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => useContext(UserContext);
