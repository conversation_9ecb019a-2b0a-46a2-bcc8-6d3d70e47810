import React, { useState, useEffect, useRef } from "react";
import { URL } from "../../../helpers/constant/Url";
import { useUser } from "../../../helpers/context/UserContext.jsx";
import siteConstant from "../../../helpers/constant/siteConstant";

// Default profile image
import dummyProfile from "../../../assets/images/dummtprofile.png";

const UserDropdown = ({ currentItems, onSelect }) => {
  // Flag to track if dropdown is visible
  const [isVisible, setIsVisible] = useState(false);
  const observer = useRef(null);
  const dropdownRef = useRef(null);
  const { selectedUser } = useUser();

  // Initialize Intersection Observer when component mounts
  useEffect(() => {
    observer.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
          // Disconnect once visible
          observer.current.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (dropdownRef.current) {
      observer.current.observe(dropdownRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, []);

  // Lazy loading image component
  const LazyImage = ({ src, alt, className, fallback }) => {
    const [imageSrc, setImageSrc] = useState(fallback);
    const [imageRef, setImageRef] = useState();

    useEffect(() => {
      let observer;
      if (imageRef && imageSrc === fallback && isVisible) {
        observer = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                setImageSrc(src || fallback);
                observer.unobserve(imageRef);
              }
            });
          },
          { threshold: 0.1 }
        );
        observer.observe(imageRef);
      }
      return () => {
        if (observer && observer.unobserve) {
          observer.disconnect();
        }
      };
    }, [imageRef, imageSrc, src, fallback, isVisible]);

    return (
      <img
        ref={setImageRef}
        src={imageSrc}
        alt={alt}
        className={className}
        onError={() => setImageSrc(fallback)}
      />
    );
  };

  // Get user status badge
  const getUserStatusBadge = (user) => {
    if (user.is_active) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          Active
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          Inactive
        </span>
      );
    }
  };

  return (
    <ul
      ref={dropdownRef}
      className="p-2 max-h-[300px] overflow-y-auto w-full sm:min-w-[300px] max-w-[100%]"
    >
      {currentItems.map((user, index) => (
        <li
          key={user.id || index}
          className={`bg-white border-gray-200 p-3 flex items-start sm:items-center gap-4 flex-wrap border hover:shadow-md transition cursor-pointer w-full
            ${
              selectedUser?.id === user.id
                ? "border-[#563D39]"
                : "border-gray-200"
            }
            ${index === 0 ? "rounded-t-lg" : ""}
            ${index === currentItems.length - 1 ? "rounded-b-lg" : ""}`}
          onClick={() => onSelect(user)}
        >
          <LazyImage
            src={user.profile_image ? `${URL.SOCKET_URL}/${user.profile_image}` : null}
            alt="User Profile"
            className="h-10 w-10 min-w-[2.5rem] rounded-full border object-cover"
            fallback={dummyProfile}
          />
          <div className="flex-1 min-w-0">
            <h3 className="text-base font-medium text-gray-900 truncate">
              {user.name || user.username || user.email}
            </h3>
            <p className="text-sm text-gray-500 mb-1 truncate">{user.email}</p>
            <div className="flex flex-wrap gap-2 items-center">
              {getUserStatusBadge(user)}
              {user.is_admin && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Admin
                </span>
              )}
            </div>
          </div>
        </li>
      ))}
    </ul>
  );
};

export default UserDropdown;
